<?php

use App\Http\Controllers\PageController;
use App\Http\Controllers\Api\AuthController;
use Illuminate\Support\Facades\Route;

Route::get('/', [PageController::class, 'home']);

// App-to-Middleware Authentication Routes (initiated by the mobile app).
// These routes participate in a browser redirect flow to the IdP (Okta).
// They live in web.php because they use session for state/PKCE storage and
// perform redirects.
Route::prefix('auth')->group(function () {
    // Initiates the Okta authorization redirect. Throttle to mitigate abuse.
    Route::get('/login', [AuthController::class, 'login'])
        ->middleware('throttle:30,1')
        ->name('auth.login');

    Route::get('/success', [AuthController::class, 'success'])->name('auth.success');
    Route::get('/error', [AuthController::class, 'error'])->name('auth.error');
});

// Middleware-to-SSO Provider Routes (Okta callbacks)
// The IdP (Okta) redirects the user's browser back to this callback after login.
// It validates state/PKCE, exchanges code for tokens, and deep-links back to the app.
// This route handles both localhost (from Okta) and IP-based (from mobile WebView) requests.
Route::prefix('sso-auth')->group(function () {
    Route::get('/callback', [AuthController::class, 'callback'])
        ->middleware('throttle:30,1')
        ->name('sso-auth.callback');
});

