<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PostsController;
use App\Http\Controllers\Api\UnaController;
use Illuminate\Support\Facades\Route;

// USER UNSECURE (only in local/testing) and throttled to mitigate abuse
if (app()->environment(['local', 'testing'])) {
    Route::post('/dev-login', [AuthController::class, 'devLogin'])
        ->middleware('throttle:5,1');
}

// Public endpoint to exchange short-lived JWT for Sanctum token; no throttling in test environment to prevent issues with rapid tests
if (app()->environment('testing')) {
    Route::post('/exchange-token', [AuthController::class, 'exchangeToken']);
} else {
    Route::post('/exchange-token', [AuthController::class, 'exchangeToken'])
        ->middleware('throttle:10,1');
}

Route::middleware('auth:sanctum')->group(function () {

    // UNA-SECURED
    Route::post('/una-logout', [UnaController::class, 'logout']);
    Route::post('/una-check-session', [UnaController::class, 'checkSession']);

    // Secured
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);

    // POSTS
    Route::post('/post', [PostsController::class, 'store']);
    Route::get('/post', [PostsController::class, 'index']);
});
