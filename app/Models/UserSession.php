<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class UserSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'okta_access_token',
        'okta_refresh_token',
        'okta_id_token',
        'okta_expires_at',
        'app_token_hash',
        'platform',
        'okta_session_id',
        'okta_user_data',
        'state',
        'code_verifier',
        'code_challenge',
        'is_active',
        'last_activity_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     * Ensure decrypted token accessors are never exposed in API responses.
     *
     * @var list<string>
     */
    protected $hidden = [
        'okta_access_token',
        'okta_refresh_token',
        'okta_id_token',
        'app_token_hash',
    ];

    protected $casts = [
        'okta_expires_at' => 'datetime',
        'is_active' => 'boolean',
        'last_activity_at' => 'datetime',
        // Don't cast okta_user_data here, we'll handle it manually for SQLite compatibility
    ];

    /**
     * Get the user that owns the session.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the Okta user data attribute (decode JSON).
     */
    public function getOktaUserDataAttribute($value)
    {
        if (is_null($value)) {
            return null;
        }

        if (is_array($value)) {
            return $value;
        }

        return json_decode($value, true);
    }

    /**
     * Set the Okta user data attribute (encode to JSON).
     */
    public function setOktaUserDataAttribute($value)
    {
        if (is_null($value)) {
            $this->attributes['okta_user_data'] = null;
            return;
        }

        if (is_array($value)) {
            $this->attributes['okta_user_data'] = json_encode($value);
            return;
        }

        $this->attributes['okta_user_data'] = $value;
    }

    /**
     * Set the Okta access token (encrypted).
     */
    public function setOktaAccessTokenAttribute($value)
    {
        $this->attributes['okta_access_token'] = $value ? Crypt::encryptString($value) : null;
    }

    /**
     * Get the Okta access token (decrypted).
     */
    public function getOktaAccessTokenAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    /**
     * Set the Okta refresh token (encrypted).
     */
    public function setOktaRefreshTokenAttribute($value)
    {
        $this->attributes['okta_refresh_token'] = $value ? Crypt::encryptString($value) : null;
    }

    /**
     * Get the Okta refresh token (decrypted).
     */
    public function getOktaRefreshTokenAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    /**
     * Set the Okta ID token (encrypted).
     */
    public function setOktaIdTokenAttribute($value)
    {
        $this->attributes['okta_id_token'] = $value ? Crypt::encryptString($value) : null;
    }

    /**
     * Get the Okta ID token (decrypted).
     */
    public function getOktaIdTokenAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    /**
     * Check if the Okta session is still valid.
     */
    public function isOktaSessionValid(): bool
    {
        if (!$this->is_active || !$this->okta_expires_at) {
            return false;
        }

        return now()->isBefore($this->okta_expires_at);
    }

    /**
     * Update the last activity timestamp.
     */
    public function updateActivity(): void
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * Deactivate the session.
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Scope to get active sessions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get sessions for a specific platform.
     */
    public function scopeForPlatform($query, string $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * Scope to get expired sessions.
     */
    public function scopeExpired($query)
    {
        return $query->where('okta_expires_at', '<', now());
    }
}
