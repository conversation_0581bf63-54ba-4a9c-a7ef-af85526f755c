<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\UnaApiService;
use Illuminate\Http\Request;

class PostsController extends Controller
{
    // Example: GET /api/posts
    public function index(UnaApiService $unaApiService)
    {
        $posts = $unaApiService->get('todos/1');

        return response()->json(['posts' => $posts]);
    }

    // Example: POST /api/posts
    public function store(Request $request)
    {
        // Handle post creation logic here
        return response()->json(['message' => 'Post created']);
    }
}
