<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\UnaApiService;
use App\Services\UnaAuthService;
use Illuminate\Http\Request;

class UnaController extends Controller
{
    protected $unaApiService;

    protected $unaAuthService;

    public function __construct(UnaApiService $unaApiService, UnaAuthService $unaAuthService)
    {
        $this->unaApiService = $unaApiService;
        $this->unaAuthService = $unaAuthService;
    }

    public function logout(Request $request)
    {
        $user = $request->user();
        $unaLogoutResponse = $this->unaAuthService->logoutFromUna($user);

        return response()->json([
            'message' => 'Logged out successfully',
            'una_logout_response' => $unaLogoutResponse,
        ]);
    }

    public function checkSession(Request $request)
    {
        $user = $request->user();
        $session = $this->unaAuthService->getUnaSession($user, false);

        return response()->json([
            'email' => $user->email,
            'account_data' => $user ? [
                'account_id' => $user->una_account_id,
                'profile_id' => $user->una_profile_id,
                'content_id' => $user->una_content_id,
            ] : null,
            'session' => $session,
        ]);
    }
}
