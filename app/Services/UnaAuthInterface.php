<?php

namespace App\Services;

use App\Models\UnaSession;
use App\Models\User;

interface UnaAuthServiceInterface
{
    public function __construct(UnaApiService $unaApiService);

    public function authenticate(User $user): ?UnaSession;

    public function getUnaSession($user): ?UnaSession;

    public function storeUnaSession(User $user, UnaSession $unaSession): bool;

    public function isSessionExpired(User $user): bool;

    public function invalidateSession(string $email): bool;
}
