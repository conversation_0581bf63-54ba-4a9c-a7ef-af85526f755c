<?php

namespace App\Services;

use App\Models\UnaSession;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class UnaAuthService
{
    private $unaApiService;

    private $sessionTtl;

    public function __construct(UnaApiService $unaApiService)
    {
        $this->unaApiService = $unaApiService;
        $this->sessionTtl = (int) config('una.session_ttl', 1440); // Default 24 hours
    }

    public function getUnaSession(User $user, bool $autoCreate = true): ?UnaSession
    {
        $session = $user->una_session_id ? new UnaSession($user, $user->una_session_id, $user->una_expires_at) : null;

        if ($session != null) {
            $expired = $this->isSessionExpired($session);
            if ($expired) {
                Log::warning("⏰ Session EXPIRED for {$user->id} - expired at {$session->expires_at}");
                if ($autoCreate) {
                    $session = $this->authenticate($user);
                }

            } else {
                Log::info("✅ Session VALID for {$user->id} - expires in {$session->expires_at->diffFor<PERSON>umans(now())}");
            }
        } elseif ($autoCreate) {
            $session = $this->authenticate($user);
        }

        return $session;
    }

    public function authenticate(User $user): ?UnaSession
    {
        Log::info("📝 Starting Una authentication: {$user->email}");
        $response = $this->unaApiService->loginToUna($user->email);
        Log::info('🔍 Extracted data from UNA response', [$response]);
        if ($response && $response['status'] === 200) {
            $data = $response['data'] ?? [];
            $sessionId = $data['session'] ?? null;
            $expiresAt = now()->addMinutes($this->sessionTtl);

            if ($sessionId) {
                $session = new UnaSession($user, $sessionId, $expiresAt);
                $this->storeUnaSession($user, $session);
                if (! $user->una_account_id) {
                    $this->storeUnaUserDetails($user, $data);
                }

                return $session;
            }
        } else {
            Log::warning('⚠️ UNA authentication failed! ', [$response]);
        }

        return null;
    }

    public function logoutFromUna(User $user): bool
    {
        Log::info("🔒 Logging out from UNA USER_ID:{$user->id}");
        $response = $this->unaApiService->logoutFromUna($user->email);
        Log::info('🔍 Extracted data from UNA logout response', [$response]);
        if ($response && $response['status'] === 200) {
            $this->invalidateSession($user);

            return true;
        }

        return false;
    }

    public function storeUnaSession(User $user, UnaSession $unaSession): bool
    {
        $user->una_session_id = $unaSession->session_id;
        $user->una_expires_at = $unaSession->expires_at;
        Log::info('💾 Una Session stored successfully', [$unaSession]);

        return $user->save();
    }

    private function storeUnaUserDetails(User $user, $data)
    {
        $user->una_account_id = $data['account_id'] ?? null;
        $user->una_profile_id = $data['profile_id'] ?? null;
        $user->una_content_id = $data['content_id'] ?? null;
        Log::info('💾 Una User Data stored successfully', [$data]);

        return $user->save();
    }

    private function isSessionExpired(UnaSession $session): bool
    {
        if ($session->expires_at == null) {
            return true;
        }
        $now = now();

        return $now->gt($session->expires_at);
    }

    public function invalidateSession(User $user): bool
    {
        $user->una_session_id = null;
        $user->save();
        Log::info('🚪 Session invalidated', [
            'user_id' => $user->id,
            'account_id' => $user->account_id,
            'profile_id' => $user->profile_id,
            'invalidated_at' => now()->toISOString(),
        ]);

        return true;
    }
}
