<?php

namespace App\Services;

use App\Models\UserSession;
use Illuminate\Support\Facades\Log;

class UserSessionService
{
    public function storeUserData(string $email, array $unaResponse): UserSession
    {
        Log::info("📝 Starting user data storage for email: {$email}");

        // Extract data from UNA response (data is nested under 'data' key)
        $data = $unaResponse['data'] ?? [];
        $sessionId = $data['session'] ?? null;
        $accountId = $data['account_id'] ?? null;
        $profileId = $data['profile_id'] ?? null;
        $contentId = $data['content_id'] ?? null;

        // Calculate expiration time using UNA_SESSION_TTL (default 60 minutes)
        $sessionTtl = (int) env('UNA_SESSION_TTL', 60); // Default 60 minutes
        $expiresAt = now()->addMinutes($sessionTtl);

        Log::info("🔍 Extracted data from UNA response", [
            'session_id' => $sessionId,
            'account_id' => $accountId,
            'profile_id' => $profileId,
            'content_id' => $contentId,
            'ttl_minutes' => $sessionTtl,
            'expires_at' => $expiresAt->toISOString()
        ]);

        // Store or update user session data
        $userSession = UserSession::updateOrCreate(
            ['email' => $email],
            [
                'session_id' => $sessionId,
                'account_id' => $accountId,
                'profile_id' => $profileId,
                'content_id' => $contentId,
                'expires_at' => $expiresAt,
            ]
        );

        Log::info("💾 User data stored successfully", [
            'email' => $userSession->email,
            'session_id' => $userSession->session_id,
            'account_id' => $userSession->account_id,
            'profile_id' => $userSession->profile_id,
            'content_id' => $userSession->content_id,
            'expires_at' => $userSession->expires_at,
            'created_at' => $userSession->created_at,
            'updated_at' => $userSession->updated_at
        ]);

        return $userSession;
    }

    /**
     * Check if a user session is expired and log the result
     */
    public function isSessionExpired(string $email): bool
    {
        $userSession = UserSession::where('email', $email)->first();
        
        if (!$userSession) {
            Log::info("⚠️ No session found for email: {$email}");
            return true;
        }

        // Check if session was invalidated (logged out)
        if (!$userSession->session_id) {
            Log::info("🚪 Session invalidated (logged out) for: {$email}");
            return true;
        }

        $now = now();
        $isExpired = $now->gt($userSession->expires_at);
        
        Log::info("🕐 Session expiration check", [
            'email' => $email,
            'current_time' => $now->toISOString(),
            'expires_at' => $userSession->expires_at->toISOString(),
            'is_expired' => $isExpired,
            'time_remaining' => $isExpired ? 'EXPIRED' : $userSession->expires_at->diffForHumans($now)
        ]);

        if ($isExpired) {
            Log::warning("⏰ Session EXPIRED for {$email} - expired at {$userSession->expires_at}");
        } else {
            Log::info("✅ Session VALID for {$email} - expires in {$userSession->expires_at->diffForHumans($now)}");
        }

        return $isExpired;
    }

    /**
     * Get user session with expiration check
     */
    public function getUserSession(string $email): ?UserSession
    {
        $userSession = UserSession::where('email', $email)->first();
        
        if (!$userSession) {
            Log::info("❌ No session record found for: {$email}");
            return null;
        }

        $isExpired = $this->isSessionExpired($email);
        
        if ($isExpired) {
            Log::warning("🗑️ Session expired - should be cleaned up for: {$email}");
            // Note: Not deleting here, just flagging for cleanup
        }

        return $userSession;
    }

    /**
     * Invalidate user session on explicit logout
     */
    public function invalidateSession(string $email): bool
    {
        $userSession = UserSession::where('email', $email)->first();
        
        if (!$userSession) {
            Log::info("⚠️ No session to invalidate for: {$email}");
            return false;
        }

        // Clear session_id but preserve user data for analytics
        $userSession->update(['session_id' => null]);
        
        Log::info("🚪 Session invalidated on logout", [
            'email' => $email,
            'account_id' => $userSession->account_id,
            'profile_id' => $userSession->profile_id,
            'invalidated_at' => now()->toISOString()
        ]);

        return true;
    }
}
