<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class UnaApiService
{
    public function loginToUna(string $email)
    {
        $baseUrl = config('una.api_domain');
        $token = config('una.api_token');

        $url = rtrim($baseUrl, '/').'/api.php';

        Log::info("UNA API Call - URL: {$url}");
        Log::info("UNA API Call - Email: {$email}");

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$token,
                'Accept' => 'application/json',
            ])->get($url, [
                'r' => 'system/login/TemplServiceContent',
                'params' => [$email],
            ]);

            Log::info('UNA API Response Status: '.$response->status());
            Log::info('UNA API Response Body: '.$response->body());

            return $response->json();

        } catch (\Exception $e) {
            Log::error('UNA API Error: '.$e->getMessage());

            return null;
        }
    }

    public function logoutFromUna(string $email)
    {
        $baseUrl = config('una.api_domain');
        $token = config('una.api_token');

        $url = rtrim($baseUrl, '/').'/api.php';

        Log::info("🚪 UNA Logout API Call - URL: {$url}");
        Log::info("🚪 UNA Logout API Call - Email: {$email}");

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$token,
                'Accept' => 'application/json',
            ])->get($url, [
                'r' => 'system/logout/TemplServiceContent',
                'params' => [$email],
            ]);

            Log::info('🚪 UNA Logout Response Status: '.$response->status());
            Log::info('🚪 UNA Logout Response Body: '.$response->body());

            return $response->json();

        } catch (\Exception $e) {
            Log::error('🚪 UNA Logout API Error: '.$e->getMessage());

            return null;
        }
    }
}
