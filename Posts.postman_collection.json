{"info": {"_postman_id": "a0a1a7d9-386b-4b82-b0c3-bccf75b15279", "name": "Posts", "description": "Sends raw Authorization and Cookie headers; uses pre-encoded params; disables URL encoding to match the working cURL.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "47578972", "_collection_link": "https://una-cms.postman.co/workspace/7112db2c-579c-4058-a33a-34742885a7cc/collection/47578972-a0a1a7d9-386b-4b82-b0c3-bccf75b15279?action=share&source=collection_link&creator=47578972"}, "item": [{"name": "New Post", "protocolProfileBehavior": {"disableUrlEncoding": true, "followRedirects": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{bearer_token}}", "type": "text"}, {"key": "<PERSON><PERSON>", "value": "memberSession={{member_session}}", "type": "text"}], "url": {"raw": "https://stride.tiltedwindmillagency.com/api.php?r=system/add/TemplServiceContent&params=%5B%22bx_posts%22%2C%7B%22title%22%3A%22Post%20title%22%2C%22text%22%3A%22Some%20text%22%2C%22cat%22%3A2%2C%22allow_view_to%22%3A3%2C%22labels%22%3A%22General%22%7D%5D", "protocol": "https", "host": ["stride", "tiltedwindmillagency", "com"], "path": ["api.php"], "query": [{"key": "r", "value": "system/add/TemplServiceContent"}, {"key": "params", "value": "%5B%22bx_posts%22%2C%7B%22title%22%3A%22Post%20title%22%2C%22text%22%3A%22Some%20text%22%2C%22cat%22%3A2%2C%22allow_view_to%22%3A3%2C%22labels%22%3A%22General%22%7D%5D"}]}}, "response": []}]}