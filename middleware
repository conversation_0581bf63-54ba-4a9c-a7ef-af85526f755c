#!/bin/bash

COMPOSE_FILE="docker-compose.dev.yaml"
SERVICE="workspace"

case "$1" in
    start)
        clear
        echo "Starting ⌛"
        docker compose -f $COMPOSE_FILE up -d
        echo "Containers started, ensuring APP_KEY is set..."
        # Call the keygen function to ensure APP_KEY is set
        $0 keygen
        echo "Started ✅"
        ;;
    stop)
        clear
        echo "Stopping ⌛"
        docker compose -f $COMPOSE_FILE down
        echo "Stopped ✅"
        ;;
    install)
        docker compose -f $COMPOSE_FILE exec $SERVICE bash -c "composer install && npm install && npm run dev"
        ;;
    migrate)
        docker compose -f $COMPOSE_FILE exec php-fpm php artisan migrate --force
        ;;
    keygen)
        docker compose -f $COMPOSE_FILE exec php-fpm bash -lc '
          set -e
          cd /var/www
          # Check if APP_KEY is already set and non-empty
          if grep -q "^APP_KEY=" .env && [ -n "$(grep "^APP_KEY=" .env | cut -d= -f2-)" ]; then
            echo "[keygen] APP_KEY already set; skipping key:generate"
          else
            echo "[keygen] Generating APP_KEY via artisan"
            php artisan key:generate
          fi'
        ;;
    exec)
        docker compose -f $COMPOSE_FILE exec $SERVICE bash
        ;;
    -i)
        echo "Available commands:"
        echo "  start    - Start the containers"
        echo "  stop     - Stop the containers"
        echo "  install  - Run composer and npm install inside the workspace"
        echo "  migrate  - Run Laravel migrations inside php-fpm (with --force)"
        echo "  keygen   - Generate Laravel APP_KEY inside php-fpm"
        echo "  exec     - Open a bash shell inside the workspace"
        echo "  clean    - Stop and remove all containers, networks, volumes, and images"
        echo "  logs     - Tail web, php-fpm, and Laravel app logs"
        echo "  man      - Show this help message"
        ;;
    clean)
      echo "Stopping and removing all containers, networks, volumes, and images defined in $COMPOSE_FILE ⌛"
        docker compose -f $COMPOSE_FILE down --volumes --remove-orphans
        docker volume prune -f
        echo "Deleted ✅"
        ;;
    logs)
        echo "Tailing nginx (web), php-fpm, and Laravel app logs..."
        docker compose -f $COMPOSE_FILE logs -f web php-fpm &
        LOGS_PID=$!
        # Ensure Laravel log file exists, then tail it with follow across rotations
        docker compose -f $COMPOSE_FILE exec php-fpm bash -lc 'mkdir -p storage/logs && touch storage/logs/laravel.log && tail -n 200 -F storage/logs/laravel.log'
        # Cleanup background docker logs on exit
        kill $LOGS_PID 2>/dev/null || true
        ;;
    *)
        echo "Usage: $0 {start|stop|install|migrate|keygen|exec|clean|ps|logs|-i}"
        exit 1
        ;;
esac
