<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application, which will be used when the
    | framework needs to place the application's name in a notification or
    | other UI elements where an application name needs to be displayed.
    |
    */
    'name' => env('APP_NAME', 'LCC-Middleware'),

    /*
    |--------------------------------------------------------------------------
    | Application Scheme
    |--------------------------------------------------------------------------
    |
    | This value is the custom URL scheme used for mobile app deep linking.
    | This is used for redirecting users back to the mobile app after
    | successful authentication with Okta.
    |
    */

    'scheme' => env('APP_SCHEME', 'learningcoachcommunity'),

    /*
    |--------------------------------------------------------------------------
    | Auth Callback Path
    |--------------------------------------------------------------------------
    |
    | This is the path component used for deep link redirects after successful
    | authentication. It will be combined with the APP_SCHEME to create the
    | complete deep link URL (e.g., myapp://auth-callback).
    |
    */

    'auth_callback_path' => env('APP_AUTH_CALLBACK_PATH', 'auth-callback'),

    /*
    |--------------------------------------------------------------------------
    | Deep Link Base (Optional)
    |--------------------------------------------------------------------------
    |
    | If set, this value is used as the base for deep link redirects, allowing
    | full URLs such as Expo Go development servers (e.g., exp://127.0.0.1:19000/--).
    | When provided, the deep link will be built as:
    |   rtrim(APP_DEEP_LINK_BASE, '/') . '/' . ltrim(APP_AUTH_CALLBACK_PATH, '/')
    |
    | If not set, the deep link falls back to scheme://auth_callback_path.
    */

    'deep_link_base' => env('APP_DEEP_LINK_BASE'),

    /*
    |--------------------------------------------------------------------------
    | Token Signing Key
    |--------------------------------------------------------------------------
    |
    | This key is used to sign short-lived JWT tokens that are passed to the
    | mobile app via deep links. These tokens should have a very short
    | expiration time (60 seconds) for security.
    |
    */

    'token_signing_key' => env('TOKEN_SIGNING_KEY'),



    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services the application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => (bool) env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | the application so that it's available within Artisan commands.
    |
    */

    'url' => env('APP_URL', 'http://localhost'),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. The timezone
    | is set to "UTC" by default as it is suitable for most use cases.
    |
    */

    'timezone' => 'UTC',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by Laravel's translation / localization methods. This option can be
    | set to any locale for which you plan to have translation strings.
    |
    */

    'locale' => env('APP_LOCALE', 'en'),

    'fallback_locale' => env('APP_FALLBACK_LOCALE', 'en'),

    'faker_locale' => env('APP_FAKER_LOCALE', 'en_US'),

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is utilized by Laravel's encryption services and should be set
    | to a random, 32 character string to ensure that all encrypted values
    | are secure. You should do this prior to deploying the application.
    |
    */

    'cipher' => 'AES-256-CBC',

    'key' => env('APP_KEY'),

    'previous_keys' => [
        ...array_filter(
            explode(',', (string) env('APP_PREVIOUS_KEYS', ''))
        ),
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode Driver
    |--------------------------------------------------------------------------
    |
    | These configuration options determine the driver used to determine and
    | manage Laravel's "maintenance mode" status. The "cache" driver will
    | allow maintenance mode to be controlled across multiple machines.
    |
    | Supported drivers: "file", "cache"
    |
    */

    'maintenance' => [
        'driver' => env('APP_MAINTENANCE_DRIVER', 'file'),
        'store' => env('APP_MAINTENANCE_STORE', 'database'),
    ],
];
