<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // This migration appears to conflict with an earlier, canonical user_sessions table.
        // Only create the table if it does not already exist to avoid duplicate table errors.
        if (!Schema::hasTable('user_sessions')) {
            Schema::create('user_sessions', function (Blueprint $table) {
                $table->string('email')->primary();
                $table->string('session_id')->nullable();
                $table->string('account_id')->nullable();
                $table->string('profile_id')->nullable();
                $table->string('content_id')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->timestamps();
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('user_sessions');
    }
};
