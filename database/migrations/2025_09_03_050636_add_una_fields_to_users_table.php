<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Determine which columns need to be added to avoid duplicate-column errors
        $needsName = !Schema::hasColumn('users', 'name');
        $needsUnaSessionId = !Schema::hasColumn('users', 'una_session_id');
        $needsUnaAccountId = !Schema::hasColumn('users', 'una_account_id');
        $needsUnaProfileId = !Schema::hasColumn('users', 'una_profile_id');
        $needsUnaContentId = !Schema::hasColumn('users', 'una_content_id');
        $needsUnaExpiresAt = !Schema::hasColumn('users', 'una_expires_at');

        Schema::table('users', function (Blueprint $table) use (
            $needsName,
            $needsUnaSessionId,
            $needsUnaAccountId,
            $needsUnaProfileId,
            $needsUnaContentId,
            $needsUnaExpiresAt
        ) {
            if ($needsName) {
                $table->string('name')->nullable();
            }
            if ($needsUnaSessionId) {
                $table->string('una_session_id')->nullable();
            }
            if ($needsUnaAccountId) {
                $table->integer('una_account_id')->nullable();
            }
            if ($needsUnaProfileId) {
                $table->integer('una_profile_id')->nullable();
            }
            if ($needsUnaContentId) {
                $table->integer('una_content_id')->nullable();
            }
            if ($needsUnaExpiresAt) {
                $table->timestamp('una_expires_at')->nullable();
            }
        });

        // Safely drop legacy column if supported (avoid failing on SQLite during tests)
        $isSqlite = Schema::getConnection()->getDriverName() === 'sqlite';
        if (!$isSqlite && Schema::hasColumn('users', 'cookie-session')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn(['cookie-session']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'name')) {
                $table->dropColumn('name');
            }
            if (Schema::hasColumn('users', 'una_session_id')) {
                $table->dropColumn('una_session_id');
            }
            if (Schema::hasColumn('users', 'una_account_id')) {
                $table->dropColumn('una_account_id');
            }
            if (Schema::hasColumn('users', 'una_profile_id')) {
                $table->dropColumn('una_profile_id');
            }
            if (Schema::hasColumn('users', 'una_content_id')) {
                $table->dropColumn('una_content_id');
            }
            if (Schema::hasColumn('users', 'una_expires_at')) {
                $table->dropColumn('una_expires_at');
            }
        });
    }
};
