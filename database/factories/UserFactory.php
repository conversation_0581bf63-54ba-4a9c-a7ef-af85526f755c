<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'remember_token' => Str::random(10),
            'una_session_id' => null,
            'una_account_id' => null,
            'una_profile_id' => null,
            'una_content_id' => null,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * State to attach populated UNA fields to the user for tests that need UNA linkage.
     */
    public function unaLinked(): static
    {
        return $this->state(fn (array $attributes) => [
            'una_session_id' => (string) Str::uuid(),
            'una_account_id' => fake()->numberBetween(1000, 9999),
            'una_profile_id' => fake()->numberBetween(10000, 99999),
            'una_content_id' => fake()->numberBetween(100000, 999999),
            'una_expires_at' => now()->addHours(2),
        ]);
    }
}
