## How to Install.
1. Paste .env
2. Install docker
2. `./middleware start`
2. `./middleware install`

## How to run
`./middleware start`

## How to stop
`./middleware stop`

## How to migrate DB:
`./middleware migrate`

## How to exec bash on docker
`./middleware exec`

## How to clean everything
`./middleware clean`

## How to see more commands?
`./middleware -i`

Additional helpers:
- `./middleware keygen` → Generate Laravel APP_KEY in php-fpm (first-run only, runs with 'start' if no key in env file)
- `./middleware logs` → Tail web and php-fpm logs

## Why APP_KEY is needed

<PERSON><PERSON> uses `APP_KEY` to secure application encryption operations. It must be set per environment and should not be changed after first generation.

- Encrypted cookies and session payloads (when using encrypted cookies/session drivers)
- The `Crypt` facade and any encrypted columns or payloads
- Framework tokens and secure random strings in certain features

If you rotate `APP_KEY`, previously encrypted cookies/sessions become invalid. Our `./middleware start` runs `keygen` safely; it only generates a key when missing and will skip if one already exists.

## Auth Architecture

This middleware implements a mobile-first OAuth flow using Okta and deep links.

- __Initiation (App → Middleware)__
  - React Native app opens a WebView to `GET /auth/login`.
  - Middleware generates `state` and PKCE, stores them in the session (DB driver), and redirects to Okta authorize URL.

- __IdP (Okta) → Middleware callback__
  - Okta redirects the browser to `GET /sso-auth/callback?code=...&state=...`.
  - Middleware validates `state`/PKCE, exchanges the code for tokens via `OktaService`, fetches the user profile, and upserts `User` + `UserSession`.

- __Deep link back to the app__
  - Middleware creates a short‑lived JWT ("deep link token") and redirects the browser to the configured deep link:
    - Example: `myapp://auth-callback?token=SHORT_TOKEN`
  - The app receives the token from the deep link.

- __Token exchange (App → Middleware)__
  - App calls `POST /auth/exchange-token` with the short token.
  - Middleware validates the token and returns a longer‑lived middleware access token (Sanctum Personal Access Token) used for subsequent API calls.

- __Authenticated API usage__
  - App calls API endpoints (e.g., `GET /api/user`, `POST /api/refresh`, `POST /api/logout`) with `Authorization: Bearer <token>`.
  - Routes are protected by `auth:sanctum` and throttled.

### Key routes and behaviors

- `routes/web.php`
  - `GET /auth/login` → starts Okta redirect.
  - `GET /sso-auth/callback` → validates, exchanges code, deep‑links back to app.
- `routes/api.php`
  - `POST /auth/exchange-token` → swaps short token for Sanctum token (CSRF disabled intentionally for this route).
  - Authenticated endpoints grouped under `auth:sanctum`.

### Configuration notes

- Sessions/Cache/Queue use database drivers by default; run migrations before using locally: `php artisan migrate`.
- Okta settings come from `.env` (`OKTA_ISSUER`, `OKTA_CLIENT_ID`, `OKTA_CLIENT_SECRET`, `OKTA_REDIRECT_URI`).
- Deep link path is configured via `.env` (`APP_AUTH_CALLBACK_PATH`), combined with your app scheme.

## Mobile dev setup

The middleware supports a mobile-first OAuth flow and deep links back into the app. During development, you can dynamically provide the deep link base per request so iOS and Android receive the correct Expo URL.

- __Dynamic deep link base__
  - The app can call `GET /auth/login?platform=mobile&deep_link_base=<URL-ENCODED-BASE>`
  - The middleware stores `deep_link_base` in the session for the flow and will redirect to:
    - `rtrim(deep_link_base, '/') + '/' + ltrim(APP_AUTH_CALLBACK_PATH, '/')`
  - Examples (Expo Go):
    - iOS simulator: `deep_link_base=exp://127.0.0.1:19000/--`
    - Android emulator: `deep_link_base=exp://********:19000/--`
    - LAN device: `deep_link_base=exp://192.168.x.y:19000/--`

- __Okta redirect URI (WebView → Middleware)__
  - The middleware derives the `redirect_uri` from the incoming request host to ensure the WebView can reach it.
  - Ensure these are in Okta Allowed Redirect URIs for local dev:
    - `http://localhost:8000/sso-auth/callback` (iOS simulator)
    - `http://********:8000/sso-auth/callback` (Android emulator)
  - Alternatively, use a single LAN/public host for both platforms to avoid multiple entries.

- __Production and dev clients__
  - For standalone or dev-client builds (not Expo Go), configure a custom scheme in the app and set:
    - `.env`: `APP_SCHEME=yourappscheme`
    - `.env`: `APP_AUTH_CALLBACK_PATH=auth-callback`
  - Then omit `deep_link_base`; the middleware will deep link to `yourappscheme://auth-callback?...`

> Note: dynamic `deep_link_base` is only honored in `local`/`testing` environments. In production, use a fixed app scheme (recommended) or a fixed deep link base via `APP_DEEP_LINK_BASE`.

- __Security notes__
  - The short-lived JWT in the deep link expires quickly (60s) and is exchanged for a Sanctum token via `POST /api/exchange-token`.
  - Ensure `TOKEN_SIGNING_KEY` is set and strong (base64-encoded ≥ 32 bytes after decoding).


## About Laravel

Laravel is a web application framework with expressive, elegant syntax. We believe development must be an enjoyable and creative experience to be truly fulfilling. Laravel takes the pain out of development by easing common tasks used in many web projects, such as:

- [Simple, fast routing engine](https://laravel.com/docs/routing).
- [Powerful dependency injection container](https://laravel.com/docs/container).
- Multiple back-ends for [session](https://laravel.com/docs/session) and [cache](https://laravel.com/docs/cache) storage.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).
- Database agnostic [schema migrations](https://laravel.com/docs/migrations).
- [Robust background job processing](https://laravel.com/docs/queues).
- [Real-time event broadcasting](https://laravel.com/docs/broadcasting).

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com)**
- **[Tighten Co.](https://tighten.co)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Redberry](https://redberry.international/laravel-development)**
- **[Active Logic](https://activelogic.com)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
