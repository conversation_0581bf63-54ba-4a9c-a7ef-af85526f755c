<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Tests\RefreshInMemoryDatabase;

class UserSessionEdgeCasesTest extends TestCase
{
    use RefreshInMemoryDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->refreshInMemoryDatabase();
    }

    public function test_create_or_update_from_okta_with_minimal_profile_data()
    {
        $minimalProfile = [
            'sub' => 'user123',
            'preferred_username' => 'user123', // Required fallback field
            // Missing email, name, and other optional fields
        ];
        $this->expectException(\InvalidArgumentException::class);
        User::createOrUpdateFromOkta($minimalProfile);
    }

    public function test_create_or_update_from_okta_with_malformed_profile_data()
    {
        // Test with various malformed data scenarios
        $malformedProfiles = [
            // Missing sub field
            ['email' => '<EMAIL>', 'name' => 'Test User'],
            // Empty sub field
            ['sub' => '', 'email' => '<EMAIL>'],
            // Null sub field
            ['sub' => null, 'email' => '<EMAIL>'],
            // Non-string sub field
            ['sub' => 123, 'email' => '<EMAIL>'],
        ];

        foreach ($malformedProfiles as $profile) {
            try {
                $user = User::createOrUpdateFromOkta($profile);
                
                // If no exception is thrown, verify the user was created/updated appropriately
                $this->assertInstanceOf(User::class, $user);
                
                // The okta_user_id should be set to whatever was in 'sub', even if malformed
                if (isset($profile['sub'])) {
                    $this->assertEquals($profile['sub'], $user->okta_user_id);
                }
            } catch (\Exception $e) {
                // If an exception is thrown, that's also acceptable behavior for malformed data
                $this->assertInstanceOf(\Exception::class, $e);
            }
        }
    }

    public function test_create_or_update_from_okta_with_duplicate_email_addresses()
    {
        // Create first user
        $profile1 = [
            'sub' => 'user1',
            'email' => '<EMAIL>',
            'name' => 'User One',
        ];
        $user1 = User::createOrUpdateFromOkta($profile1);

        // Try to create second user with same email but different sub
        // This should throw an exception due to unique email constraint
        $profile2 = [
            'sub' => 'user2',
            'email' => '<EMAIL>',
            'name' => 'User Two',
        ];

        $this->expectException(\Illuminate\Database\UniqueConstraintViolationException::class);
        $user2 = User::createOrUpdateFromOkta($profile2);
    }

    public function test_active_user_sessions_with_mixed_active_inactive_sessions()
    {
        $user = User::factory()->create();

        // Create mixed sessions
        $activeSession1 = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'token1',
            'okta_refresh_token' => 'refresh1',
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'app-token-1'),
            'is_active' => true,
        ]);

        $inactiveSession = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'web',
            'okta_access_token' => 'token2',
            'okta_refresh_token' => 'refresh2',
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'app-token-2'),
            'is_active' => false,
        ]);

        $activeSession2 = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'desktop',
            'okta_access_token' => 'token3',
            'okta_refresh_token' => 'refresh3',
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'app-token-3'),
            'is_active' => true,
        ]);

        $activeSessions = $user->activeUserSessions()->get();

        $this->assertCount(2, $activeSessions);
        $this->assertTrue($activeSessions->contains($activeSession1));
        $this->assertTrue($activeSessions->contains($activeSession2));
        $this->assertFalse($activeSessions->contains($inactiveSession));
    }

    public function test_get_active_session_for_platform_with_no_sessions()
    {
        $user = User::factory()->create();

        $session = $user->getActiveSessionForPlatform('mobile');

        $this->assertNull($session);
    }

    public function test_get_active_session_for_platform_with_multiple_platforms()
    {
        $user = User::factory()->create();

        // Create sessions for different platforms
        $mobileSession = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'mobile-token',
            'okta_refresh_token' => 'mobile-refresh',
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'mobile-app-token'),
            'is_active' => true,
        ]);

        $webSession = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'web',
            'okta_access_token' => 'web-token',
            'okta_refresh_token' => 'web-refresh',
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'web-app-token'),
            'is_active' => true,
        ]);

        // Test getting session for specific platform
        $retrievedMobileSession = $user->getActiveSessionForPlatform('mobile');
        $retrievedWebSession = $user->getActiveSessionForPlatform('web');
        $nonExistentSession = $user->getActiveSessionForPlatform('desktop');

        $this->assertEquals($mobileSession->id, $retrievedMobileSession->id);
        $this->assertEquals($webSession->id, $retrievedWebSession->id);
        $this->assertNull($nonExistentSession);
    }

    public function test_has_active_okta_session_with_expired_sessions()
    {
        $user = User::factory()->create();

        // Create expired session first
        $expiredSession = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'expired-token',
            'okta_refresh_token' => 'expired-refresh',
            'okta_expires_at' => now()->subHour(), // Expired
            'app_token_hash' => hash('sha256', 'expired-app-token'),
            'is_active' => true,
        ]);

        // Wait a moment to ensure different timestamps
        sleep(1);

        // Create valid session (should be latest due to later created_at)
        $validSession = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'web',
            'okta_access_token' => 'valid-token',
            'okta_refresh_token' => 'valid-refresh',
            'okta_expires_at' => now()->addHour(), // Valid
            'app_token_hash' => hash('sha256', 'valid-app-token'),
            'is_active' => true,
        ]);

        // Test individual session validity
        $this->assertFalse($expiredSession->isOktaSessionValid());
        $this->assertTrue($validSession->isOktaSessionValid());

        // Test user has active session - hasActiveOktaSession() checks the latest active session
        // Since validSession was created after expiredSession, it should be the latest
        $this->assertTrue($user->hasActiveOktaSession());

        // Deactivate the valid session
        $validSession->deactivate();
        $user->refresh();

        // Now user should not have active session (only expired session remains active)
        $this->assertFalse($user->hasActiveOktaSession());
    }

    public function test_okta_profile_data_with_nested_json_structures()
    {
        $complexProfile = [
            'sub' => 'user123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'custom_claims' => [
                'department' => 'Engineering',
                'roles' => ['admin', 'user', 'developer'],
                'permissions' => [
                    'read' => true,
                    'write' => true,
                    'delete' => false,
                ],
                'metadata' => [
                    'created_at' => '2023-01-01T00:00:00Z',
                    'last_login' => '2023-12-01T12:00:00Z',
                    'preferences' => [
                        'theme' => 'dark',
                        'language' => 'en',
                        'notifications' => [
                            'email' => true,
                            'push' => false,
                            'sms' => true,
                        ],
                    ],
                ],
            ],
        ];

        $user = User::createOrUpdateFromOkta($complexProfile);

        $this->assertEquals($complexProfile, $user->okta_profile_data);
        $this->assertEquals('Engineering', $user->okta_profile_data['custom_claims']['department']);
        $this->assertEquals(['admin', 'user', 'developer'], $user->okta_profile_data['custom_claims']['roles']);
        $this->assertTrue($user->okta_profile_data['custom_claims']['permissions']['read']);
        $this->assertEquals('dark', $user->okta_profile_data['custom_claims']['metadata']['preferences']['theme']);
        $this->assertTrue($user->okta_profile_data['custom_claims']['metadata']['preferences']['notifications']['email']);
    }

    public function test_is_okta_session_valid_with_null_expiry_dates()
    {
        $user = User::factory()->create();

        $sessionWithNullExpiry = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'token',
            'okta_refresh_token' => 'refresh',
            'okta_expires_at' => null, // Null expiry
            'app_token_hash' => hash('sha256', 'app-token'),
            'is_active' => true,
        ]);

        // Session with null expiry should be considered invalid
        $this->assertFalse($sessionWithNullExpiry->isOktaSessionValid());
    }

    public function test_is_okta_session_valid_with_past_expiry_dates()
    {
        $user = User::factory()->create();

        $expiredSession = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'token',
            'okta_refresh_token' => 'refresh',
            'okta_expires_at' => now()->subMinutes(30), // 30 minutes ago
            'app_token_hash' => hash('sha256', 'app-token'),
            'is_active' => true,
        ]);

        $this->assertFalse($expiredSession->isOktaSessionValid());
    }

    public function test_update_activity_multiple_times_in_succession()
    {
        $user = User::factory()->create();

        $session = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'token',
            'okta_refresh_token' => 'refresh',
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'app-token'),
            'is_active' => true,
        ]);

        $originalUpdatedAt = $session->updated_at;

        // Wait a moment to ensure timestamp difference
        sleep(1);

        // Update activity multiple times
        $session->updateActivity();
        $firstUpdate = $session->updated_at;

        sleep(1);

        $session->updateActivity();
        $secondUpdate = $session->updated_at;

        sleep(1);

        $session->updateActivity();
        $thirdUpdate = $session->updated_at;

        // Each update should have a newer timestamp
        $this->assertTrue($firstUpdate->gt($originalUpdatedAt));
        $this->assertTrue($secondUpdate->gt($firstUpdate));
        $this->assertTrue($thirdUpdate->gt($secondUpdate));
    }

    public function test_deactivate_on_already_inactive_sessions()
    {
        $user = User::factory()->create();

        $session = UserSession::create([
            'user_id' => $user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'token',
            'okta_refresh_token' => 'refresh',
            'okta_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'app-token'),
            'is_active' => false, // Already inactive
        ]);

        $this->assertFalse($session->is_active);

        // Deactivate already inactive session
        $session->deactivate();

        // Should remain inactive without issues
        $this->assertFalse($session->is_active);
        $session->refresh();
        $this->assertFalse($session->is_active);
    }
}
