<?php

namespace Tests\Unit;

use App\Services\OktaService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class OktaServiceTest extends TestCase
{
    protected $client;
    protected $oktaService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set test configuration
        config([
            'services.okta.domain' => 'test-domain.okta.com',
            'services.okta.client_id' => 'test_client_id',
            'services.okta.client_secret' => 'test_client_secret',
            'services.okta.redirect_uri' => 'http://localhost/auth/callback',
            'services.okta.issuer' => null, // Force using domain + auth_server_id
        ]);

        $this->client = Mockery::mock(Client::class);
        $this->oktaService = new OktaService();
        
        // Replace the client in the service
        $reflection = new \ReflectionClass($this->oktaService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->oktaService, $this->client);
    }

    public function test_generate_pkce_challenge_returns_valid_format()
    {
        $pkce = $this->oktaService->generatePkceChallenge();

        $this->assertArrayHasKey('code_verifier', $pkce);
        $this->assertArrayHasKey('code_challenge', $pkce);
        $this->assertEquals(128, strlen($pkce['code_verifier']));
        $this->assertNotEmpty($pkce['code_challenge']);
    }

    public function test_build_authorization_url_contains_required_parameters()
    {
        $state = 'test_state';
        $pkce = ['code_challenge' => 'test_challenge'];

        $url = $this->oktaService->buildAuthorizationUrl($state, $pkce);

        $this->assertStringContainsString('https://test-domain.okta.com/oauth2/default/v1/authorize', $url);
        $this->assertStringContainsString('client_id=test_client_id', $url);
        $this->assertStringContainsString('response_type=code', $url);
        $this->assertStringContainsString('scope=openid+profile+email', $url);
        $this->assertStringContainsString('redirect_uri=http%3A%2F%2Flocalhost%2Fauth%2Fcallback', $url);
        $this->assertStringContainsString('state=test_state', $url);
        $this->assertStringContainsString('code_challenge=test_challenge', $url);
        $this->assertStringContainsString('code_challenge_method=S256', $url);
    }

    public function test_exchange_code_for_tokens_success()
    {
        $responseData = [
            'access_token' => 'test_access_token',
            'refresh_token' => 'test_refresh_token',
            'id_token' => 'test_id_token',
            'expires_in' => 3600,
        ];

        $response = new Response(200, [], json_encode($responseData));
        
        $this->client->shouldReceive('post')
            ->once()
            ->with(
                'https://test-domain.okta.com/oauth2/default/v1/token',
                Mockery::on(function ($options) {
                    return isset($options['form_params']) &&
                           $options['form_params']['grant_type'] === 'authorization_code' &&
                           $options['form_params']['client_id'] === 'test_client_id' &&
                           $options['form_params']['code'] === 'test_code' &&
                           $options['form_params']['code_verifier'] === 'test_verifier';
                })
            )
            ->andReturn($response);

        $tokens = $this->oktaService->exchangeCodeForTokens('test_code', 'test_verifier');

        $this->assertEquals($responseData, $tokens);
    }

    public function test_exchange_code_for_tokens_throws_exception_on_error()
    {
        $this->client->shouldReceive('post')
            ->once()
            ->andThrow(new RequestException('Error', new Request('POST', 'test')));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to exchange code for tokens');

        $this->oktaService->exchangeCodeForTokens('test_code', 'test_verifier');
    }

    public function test_get_user_profile_success()
    {
        $profileData = [
            'sub' => 'okta_user_123',
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'email_verified' => true,
        ];

        $response = new Response(200, [], json_encode($profileData));
        
        $this->client->shouldReceive('get')
            ->once()
            ->with(
                'https://test-domain.okta.com/oauth2/default/v1/userinfo',
                Mockery::on(function ($options) {
                    return isset($options['headers']['Authorization']) &&
                           $options['headers']['Authorization'] === 'Bearer test_access_token';
                })
            )
            ->andReturn($response);

        $profile = $this->oktaService->getUserProfile('test_access_token');

        $this->assertEquals($profileData, $profile);
    }

    public function test_get_user_profile_throws_exception_on_invalid_response()
    {
        $response = new Response(200, [], json_encode(['invalid' => 'response']));
        
        $this->client->shouldReceive('get')
            ->once()
            ->andReturn($response);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid user profile received from Okta');

        $this->oktaService->getUserProfile('test_access_token');
    }

    public function test_refresh_access_token_success()
    {
        $responseData = [
            'access_token' => 'new_access_token',
            'expires_in' => 3600,
        ];

        $response = new Response(200, [], json_encode($responseData));
        
        $this->client->shouldReceive('post')
            ->once()
            ->with(
                'https://test-domain.okta.com/oauth2/default/v1/token',
                Mockery::on(function ($options) {
                    return $options['form_params']['grant_type'] === 'refresh_token' &&
                           $options['form_params']['refresh_token'] === 'test_refresh_token';
                })
            )
            ->andReturn($response);

        $tokens = $this->oktaService->refreshAccessToken('test_refresh_token');

        $this->assertEquals($responseData, $tokens);
    }

    public function test_revoke_token_success()
    {
        $response = new Response(200);
        
        $this->client->shouldReceive('post')
            ->once()
            ->with(
                'https://test-domain.okta.com/oauth2/default/v1/revoke',
                Mockery::on(function ($options) {
                    return $options['form_params']['token'] === 'test_token' &&
                           $options['form_params']['token_type_hint'] === 'access_token';
                })
            )
            ->andReturn($response);

        $result = $this->oktaService->revokeToken('test_token', 'access_token');

        $this->assertTrue($result);
    }

    public function test_revoke_token_returns_false_on_error()
    {
        $this->client->shouldReceive('post')
            ->once()
            ->andThrow(new RequestException('Error', new Request('POST', 'test')));

        $result = $this->oktaService->revokeToken('test_token');

        $this->assertFalse($result);
    }

    public function test_introspect_token_success()
    {
        $responseData = [
            'active' => true,
            'client_id' => 'test_client_id',
            'exp' => time() + 3600,
        ];

        $response = new Response(200, [], json_encode($responseData));
        
        $this->client->shouldReceive('post')
            ->once()
            ->andReturn($response);

        $result = $this->oktaService->introspectToken('test_token');

        $this->assertEquals($responseData, $result);
    }

    public function test_introspect_token_returns_inactive_on_error()
    {
        $this->client->shouldReceive('post')
            ->once()
            ->andThrow(new RequestException('Error', new Request('POST', 'test')));

        $result = $this->oktaService->introspectToken('test_token');

        $this->assertEquals(['active' => false], $result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
