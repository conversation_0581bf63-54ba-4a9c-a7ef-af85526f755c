<?php

namespace Tests\Unit;

use App\Services\OktaService;
use Tests\TestCase;

class ConfigurationValidationTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_behavior_with_missing_okta_domain()
    {
        config(['services.okta.domain' => null]);

        // OktaService should handle null config gracefully by casting to empty string
        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
    }

    public function test_behavior_with_empty_okta_domain()
    {
        config(['services.okta.domain' => '']);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
    }

    public function test_behavior_with_invalid_okta_domain_format()
    {
        config(['services.okta.domain' => 'invalid-domain-format']);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);

        // The service should still work - we just test it doesn't crash
        $url = $service->buildAuthorizationUrl('test-state', ['code_challenge' => 'test']);
        $this->assertIsString($url);
        $this->assertNotEmpty($url);
    }

    public function test_behavior_with_missing_client_credentials()
    {
        config([
            'services.okta.domain' => 'test.okta.com',
            'services.okta.client_id' => null,
            'services.okta.client_secret' => null,
        ]);

        // OktaService should handle null config gracefully by casting to empty string
        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
    }

    public function test_behavior_with_empty_client_credentials()
    {
        config([
            'services.okta.domain' => 'test.okta.com',
            'services.okta.client_id' => '',
            'services.okta.client_secret' => '',
        ]);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
    }

    public function test_behavior_with_missing_redirect_uri()
    {
        config([
            'services.okta.domain' => 'test.okta.com',
            'services.okta.client_id' => 'test-client',
            'services.okta.client_secret' => 'test-secret',
            'services.okta.redirect_uri' => null,
        ]);

        // OktaService should handle null config gracefully by casting to empty string
        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
    }

    public function test_behavior_with_invalid_redirect_uri_format()
    {
        config([
            'services.okta.domain' => 'test.okta.com',
            'services.okta.client_id' => 'test-client',
            'services.okta.client_secret' => 'test-secret',
            'services.okta.redirect_uri' => 'invalid-uri-format',
        ]);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
        
        // The service should work but the redirect URI might be invalid
        $url = $service->buildAuthorizationUrl('test-state', ['code_challenge' => 'test']);
        $this->assertStringContainsString('invalid-uri-format', $url);
    }

    public function test_configuration_validation_with_all_required_fields()
    {
        config([
            'services.okta.domain' => 'test.okta.com',
            'services.okta.client_id' => 'test-client-id',
            'services.okta.client_secret' => 'test-client-secret',
            'services.okta.redirect_uri' => 'https://localhost/callback',
            'services.okta.auth_server_id' => 'default',
        ]);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);

        // Test that service can build URLs without errors
        $url = $service->buildAuthorizationUrl('test-state', ['code_challenge' => 'test']);
        $this->assertIsString($url);
        $this->assertNotEmpty($url);
        $this->assertStringContainsString('oauth2', $url);
        $this->assertStringContainsString('authorize', $url);
    }

    public function test_fallback_behaviors_with_missing_optional_config()
    {
        config([
            'services.okta.domain' => 'test.okta.com',
            'services.okta.client_id' => 'test-client-id',
            'services.okta.client_secret' => 'test-client-secret',
            'services.okta.redirect_uri' => 'https://localhost/callback',
            'services.okta.auth_server_id' => 'default', // Provide default value
            'services.okta.issuer' => null, // Should be constructed from domain + auth_server_id
        ]);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);

        // Test that service works with default configuration
        $url = $service->buildAuthorizationUrl('test-state', ['code_challenge' => 'test']);
        $this->assertIsString($url);
        $this->assertStringContainsString('/oauth2/', $url);
        $this->assertStringContainsString('/authorize', $url);
    }

    public function test_behavior_with_different_environment_configurations()
    {
        // Test development environment configuration
        config([
            'app.env' => 'local',
            'services.okta.domain' => 'dev-test.okta.com',
            'services.okta.client_id' => 'dev-client-id',
            'services.okta.client_secret' => 'dev-client-secret',
            'services.okta.redirect_uri' => 'http://localhost:8000/callback',
        ]);

        $service = new OktaService();
        $url = $service->buildAuthorizationUrl('test-state', ['code_challenge' => 'test']);
        $this->assertIsString($url);
        $this->assertNotEmpty($url);

        // Test production environment configuration
        config([
            'app.env' => 'production',
            'services.okta.domain' => 'prod-test.okta.com',
            'services.okta.client_id' => 'prod-client-id',
            'services.okta.client_secret' => 'prod-client-secret',
            'services.okta.redirect_uri' => 'https://api.example.com/callback',
        ]);

        $service = new OktaService();
        $url = $service->buildAuthorizationUrl('test-state', ['code_challenge' => 'test']);
        $this->assertIsString($url);
        $this->assertNotEmpty($url);
    }

    public function test_configuration_with_custom_issuer()
    {
        config([
            'services.okta.domain' => 'test.okta.com',
            'services.okta.client_id' => 'test-client-id',
            'services.okta.client_secret' => 'test-client-secret',
            'services.okta.redirect_uri' => 'https://localhost/callback',
            'services.okta.issuer' => 'https://custom.okta.com/oauth2/custom',
        ]);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
        
        // When custom issuer is provided, it should be used instead of constructed one
        $issuer = $service->getIssuer();
        $this->assertEquals('https://custom.okta.com/oauth2/custom', $issuer);
    }

    public function test_configuration_validation_edge_cases()
    {
        // Test with very long configuration values
        $longDomain = str_repeat('a', 100) . '.okta.com';
        $longClientId = str_repeat('b', 200);
        $longClientSecret = str_repeat('c', 300);
        $longRedirectUri = 'https://' . str_repeat('d', 100) . '.com/callback';

        config([
            'services.okta.domain' => $longDomain,
            'services.okta.client_id' => $longClientId,
            'services.okta.client_secret' => $longClientSecret,
            'services.okta.redirect_uri' => $longRedirectUri,
        ]);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);

        // Test that service can handle long configuration values
        $url = $service->buildAuthorizationUrl('test-state', ['code_challenge' => 'test']);
        $this->assertIsString($url);
        $this->assertNotEmpty($url);
    }

    public function test_configuration_with_special_characters()
    {
        config([
            'services.okta.domain' => 'test-domain.okta.com',
            'services.okta.client_id' => 'client-id-with-special-chars!@#$%',
            'services.okta.client_secret' => 'secret-with-special-chars!@#$%^&*()',
            'services.okta.redirect_uri' => 'https://localhost/callback?param=value&other=test',
        ]);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);

        $url = $service->buildAuthorizationUrl('test-state', ['code_challenge' => 'test']);
        // Test that service can handle special characters without crashing
        $this->assertIsString($url);
        $this->assertNotEmpty($url);
        $this->assertStringContainsString('client-id-with-special-chars', $url);
    }

    public function test_app_configuration_validation()
    {
        // Test with missing app configuration
        config([
            'app.scheme' => null,
            'app.auth_callback_path' => null,
            'app.token_signing_key' => null,
        ]);

        // These should not cause the OktaService to fail
        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
    }

    public function test_app_configuration_with_valid_values()
    {
        config([
            'app.scheme' => 'myapp',
            'app.auth_callback_path' => 'auth-callback',
            'app.token_signing_key' => 'base64:' . base64_encode('test-key-32-characters-long-key'),
        ]);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
        
        // Test that the configuration is accessible
        $this->assertEquals('myapp', config('app.scheme'));
        $this->assertEquals('auth-callback', config('app.auth_callback_path'));
        $this->assertNotNull(config('app.token_signing_key'));
    }

    public function test_una_api_configuration_validation()
    {
        // Test with missing UNA API configuration
        config([
            'una.api_domain' => null,
            'una.api_token' => null,
        ]);

        // This should not affect OktaService
        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
    }

    public function test_una_api_configuration_with_valid_values()
    {
        config([
            'una.api_domain' => 'https://api.una.com',
            'una.api_token' => 'test-una-api-token',
        ]);

        $service = new OktaService();
        $this->assertInstanceOf(OktaService::class, $service);
        
        // Test that UNA configuration is accessible
        $this->assertEquals('https://api.una.com', config('una.api_domain'));
        $this->assertEquals('test-una-api-token', config('una.api_token'));
    }
}
