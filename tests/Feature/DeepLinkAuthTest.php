<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\OktaService;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Mockery;
use Tests\TestCase;
use Tests\RefreshInMemoryDatabase;

class DeepLinkAuthTest extends TestCase
{
    use RefreshInMemoryDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->refreshInMemoryDatabase();
    }

    public function test_callback_creates_short_lived_token_and_redirects_to_deep_link()
    {
        // Set up configuration
        config([
            'services.okta.issuer' => 'https://test.okta.com/oauth2/default',
            'services.okta.client_id' => 'test_client_id',
            'services.okta.client_secret' => 'test_client_secret',
            'services.okta.redirect_uri' => 'http://localhost/sso-auth/callback',
            'app.scheme' => 'testapp',
            'app.auth_callback_path' => 'auth-callback',

            // Use plain base64-encoded key (no prefix) since tests decode it directly
            'app.token_signing_key' => base64_encode('test-signing-key-32-characters-long'),
        ]);

        // Set up session data
        Session::put([
            'oauth_state' => 'test-state',
            'code_verifier' => 'test-verifier',
            'auth_platform' => 'mobile'
        ]);

        // Mock the OktaService
        $oktaService = Mockery::mock(OktaService::class);
        $this->app->instance(OktaService::class, $oktaService);

        // Mock token exchange
        $oktaService->shouldReceive('exchangeCodeForTokens')
            ->once()
            ->with('test-code', 'test-verifier')
            ->andReturn([
                'access_token' => 'test-access-token',
                'id_token' => 'test-id-token',
                'refresh_token' => 'test-refresh-token',
                'expires_in' => 3600,
            ]);

        // Mock user profile fetch
        $oktaService->shouldReceive('getUserProfile')
            ->once()
            ->with('test-access-token')
            ->andReturn([
                'sub' => 'test-user-id',
                'email' => '<EMAIL>',
                'name' => 'Test User',
            ]);

        // Make callback request
        $response = $this->get('/sso-auth/callback?code=test-code&state=test-state');

        // Assert redirect to deep link
        $response->assertStatus(302);
        $this->assertStringStartsWith('testapp://auth-callback?token=', $response->headers->get('Location'));

        // Verify token can be decoded
        $location = $response->headers->get('Location');
        parse_str(parse_url($location, PHP_URL_QUERY), $params);
        
        $this->assertArrayHasKey('token', $params);
        $this->assertArrayHasKey('user', $params);

        // Decode and verify JWT token
        $token = urldecode($params['token']);
        $key = base64_decode(config('app.token_signing_key'));
        $decoded = JWT::decode($token, new Key($key, 'HS256'));

        $this->assertEquals('test-user-id', $decoded->sub);
        $this->assertEquals('<EMAIL>', $decoded->email);
        $this->assertEquals('Test User', $decoded->name);
        
        // Verify token expires in ~60 seconds
        $this->assertLessThanOrEqual(time() + 61, $decoded->exp);
        $this->assertGreaterThanOrEqual(time() + 59, $decoded->exp);
    }

    public function test_callback_handles_missing_code_error()
    {
        config([
            'app.scheme' => 'testapp',
            'app.auth_callback_path' => 'auth-callback',

        ]);

        Session::put('oauth_state', 'test-state');

        $response = $this->get('/sso-auth/callback?state=test-state');

        $response->assertStatus(302);
        $location = $response->headers->get('Location');
        $this->assertStringStartsWith('testapp://auth-callback?error=', $location);
    }

    public function test_callback_handles_invalid_state_error()
    {
        config([
            'app.scheme' => 'testapp',
            'app.auth_callback_path' => 'auth-callback',

        ]);

        Session::put('oauth_state', 'correct-state');

        $response = $this->get('/sso-auth/callback?code=test-code&state=wrong-state');

        $response->assertStatus(302);
        $location = $response->headers->get('Location');
        $this->assertStringStartsWith('testapp://auth-callback?error=', $location);
    }

    public function test_callback_uses_configured_scheme()
    {
        config([
            'app.scheme' => 'exp', // Test with Expo Go scheme
            'app.auth_callback_path' => 'auth-callback',
        ]);

        Session::put('oauth_state', 'test-state');

        $response = $this->get('/sso-auth/callback?state=test-state');

        $response->assertStatus(302);
        $location = $response->headers->get('Location');
        $this->assertStringStartsWith('exp://auth-callback?error=', $location);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
