<?php

namespace Tests\Feature;

use App\Models\UnaSession;
use App\Models\User;
use App\Services\UnaAuthService;
use Tests\TestCase;

class AuthControllerTest extends TestCase
{
    /**
     * A basic feature test example.
     */
    public function test_example(): void
    {
        $response = $this->get('/');

        $response->assertStatus(200);
    }

    public function login_logs_in_existing_user_when_una_session_ok(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $mock = \Mockery::mock(UnaAuthService::class);
        $mock->shouldReceive('getUnaSession')
            ->once()
            ->with(\Mockery::on(fn ($u) => $u instanceof User && $u->id === $user->id))
            ->andReturn(new UnaSession($user, 'dummy_session_id', now()->addMinutes(30)));

        $this->app->instance(UnaAuthService::class, $mock);

        $response = $this->postJson('/login', ['email' => $user->email]);

        $response->assertOk()
            ->assertJsonPath('message', 'Logged in as user')
            ->assertJsonStructure(['session']);

        $this->assertAuthenticatedAs($user);

        $user->refresh();
        $this->assertSame(1, $user->tokens()->count(), 'Sanctum token should be created');
    }
}
